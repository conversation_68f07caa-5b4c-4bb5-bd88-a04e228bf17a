Metadata-Version: 2.1
Name: WTForms
Version: 3.0.1
Summary: Form validation and rendering for Python web development.
Home-page: https://wtforms.readthedocs.io/
Maintainer: WTForms
Maintainer-email: <EMAIL>
License: BSD-3-Clause
Project-URL: Documentation, https://wtforms.readthedocs.io/
Project-URL: Code, https://github.com/wtforms/wtforms
Project-URL: Issue Tracker, https://github.com/wtforms/wtforms/issues
Platform: UNKNOWN
Classifier: Development Status :: 5 - Production/Stable
Classifier: Environment :: Web Environment
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: BSD License
Classifier: Operating System :: OS Independent
Classifier: Programming Language :: Python
Classifier: Topic :: Internet :: WWW/HTTP :: Dynamic Content
Requires-Python: >=3.7
Description-Content-Type: text/x-rst
License-File: LICENSE.rst
Requires-Dist: MarkupSafe
Provides-Extra: email
Requires-Dist: email-validator ; extra == 'email'

WTForms
=======

WTForms is a flexible forms validation and rendering library for Python
web development. It can work with whatever web framework and template
engine you choose. It supports data validation, CSRF protection,
internationalization (I18N), and more. There are various community
libraries that provide closer integration with popular frameworks.


Installation
------------

Install and update using pip:

.. code-block:: text

    pip install -U WTForms


Third-Party Library Integrations
--------------------------------

WTForms is designed to work with any web framework and template engine.
There are a number of community-provided libraries that make integrating
with frameworks even better.

-   `Flask-WTF`_ integrates with the Flask framework. It can
    automatically load data from the request, uses Flask-Babel to
    translate based on user-selected locale, provides full-application
    CSRF, and more.
-   `WTForms-Alchemy`_ provides rich support for generating forms from
    SQLAlchemy models, including an expanded set of fields and
    validators.
-   `WTForms-SQLAlchemy`_ provides ORM-backed fields and form generation
    from SQLAlchemy models.
-   `WTForms-AppEngine`_ provides ORM-backed fields and form generation
    from AppEnding db/ndb schema
-   `WTForms-Django`_ provides ORM-backed fields and form generation
    from Django models, as well as integration with Django's I18N
    support.
-   `Starlette-WTF`_ integrates with Starlette and the FastAPI
    framework, based on the features of Flask-WTF.

.. _Flask-WTF: https://flask-wtf.readthedocs.io/
.. _WTForms-Alchemy: https://wtforms-alchemy.readthedocs.io/
.. _WTForms-SQLAlchemy: https://github.com/wtforms/wtforms-sqlalchemy
.. _WTForms-AppEngine: https://github.com/wtforms/wtforms-appengine
.. _WTForms-Django: https://github.com/wtforms/wtforms-django
.. _Starlette-WTF: https://github.com/muicss/starlette-wtf


Links
-----

-   Documentation: https://wtforms.readthedocs.io/
-   Releases: https://pypi.org/project/WTForms/
-   Code: https://github.com/wtforms/wtforms
-   Issue tracker: https://github.com/wtforms/wtforms/issues
-   Discord Chat: https://discord.gg/F65P7Z9


