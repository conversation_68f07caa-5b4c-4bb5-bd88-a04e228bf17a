# Spanish translations for WTForms.
# Copyright (C) 2020 WTForms Team
# This file is distributed under the same license as the WTForms project.
# <AUTHOR> <EMAIL>, 2020.
#
msgid ""
msgstr ""
"Project-Id-Version: WTForms 1.0\n"
"Report-Msgid-Bugs-To: <EMAIL>\n"
"POT-Creation-Date: 2020-04-25 11:34-0700\n"
"PO-Revision-Date: 2012-04-10 18:10+0100\n"
"Last-Translator: Syrus <PERSON> <<EMAIL>>\n"
"Language: es\n"
"Language-Team: es <<EMAIL>>\n"
"Plural-Forms: nplurals=2; plural=(n != 1)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.8.0\n"

#: src/wtforms/validators.py:87
#, python-format
msgid "Invalid field name '%s'."
msgstr "Nombre de campo inválido '%s'."

#: src/wtforms/validators.py:98
#, python-format
msgid "Field must be equal to %(other_name)s."
msgstr "El campo debe coincidir con %(other_name)s."

#: src/wtforms/validators.py:134
#, python-format
msgid "Field must be at least %(min)d character long."
msgid_plural "Field must be at least %(min)d characters long."
msgstr[0] "El campo debe tener al menos %(min)d caracter."
msgstr[1] "El campo debe tener al menos %(min)d caracteres."

#: src/wtforms/validators.py:140
#, python-format
msgid "Field cannot be longer than %(max)d character."
msgid_plural "Field cannot be longer than %(max)d characters."
msgstr[0] "El campo no puede tener más de %(max)d caracter."
msgstr[1] "El campo no puede tener más de %(max)d caracteres."

#: src/wtforms/validators.py:146
#, python-format
msgid "Field must be exactly %(max)d character long."
msgid_plural "Field must be exactly %(max)d characters long."
msgstr[0] ""
msgstr[1] ""

#: src/wtforms/validators.py:152
#, python-format
msgid "Field must be between %(min)d and %(max)d characters long."
msgstr "El campo debe tener entre %(min)d y %(max)d caracteres."

#: src/wtforms/validators.py:197
#, python-format
msgid "Number must be at least %(min)s."
msgstr "El número debe ser mayor que %(min)s."

#: src/wtforms/validators.py:199
#, python-format
msgid "Number must be at most %(max)s."
msgstr "El número debe ser menor que %(max)s."

#: src/wtforms/validators.py:201
#, python-format
msgid "Number must be between %(min)s and %(max)s."
msgstr "El número debe estar entre %(min)s y %(max)s."

#: src/wtforms/validators.py:269 src/wtforms/validators.py:294
msgid "This field is required."
msgstr "Este campo es obligatorio."

#: src/wtforms/validators.py:327
msgid "Invalid input."
msgstr "Valor inválido."

#: src/wtforms/validators.py:387
msgid "Invalid email address."
msgstr "Email inválido."

#: src/wtforms/validators.py:423
msgid "Invalid IP address."
msgstr "Dirección IP inválida."

#: src/wtforms/validators.py:466
msgid "Invalid Mac address."
msgstr "Dirección MAC inválida."

#: src/wtforms/validators.py:501
msgid "Invalid URL."
msgstr "URL inválida."

#: src/wtforms/validators.py:522
msgid "Invalid UUID."
msgstr "UUID inválido."

#: src/wtforms/validators.py:553
#, python-format
msgid "Invalid value, must be one of: %(values)s."
msgstr "Valor inválido, debe ser uno de: %(values)s."

#: src/wtforms/validators.py:588
#, python-format
msgid "Invalid value, can't be any of: %(values)s."
msgstr "Valor inválido, no puede ser ninguno de: %(values)s."

#: src/wtforms/csrf/core.py:96
msgid "Invalid CSRF Token."
msgstr "El token CSRF es incorrecto"

#: src/wtforms/csrf/session.py:63
msgid "CSRF token missing."
msgstr "El token CSRF falta"

#: src/wtforms/csrf/session.py:71
msgid "CSRF failed."
msgstr "Fallo CSRF"

#: src/wtforms/csrf/session.py:76
msgid "CSRF token expired."
msgstr "El token CSRF ha expirado"

#: src/wtforms/fields/core.py:534
msgid "Invalid Choice: could not coerce."
msgstr "Elección inválida: no se puede ajustar"

#: src/wtforms/fields/core.py:538
msgid "Choices cannot be None."
msgstr ""

#: src/wtforms/fields/core.py:545
msgid "Not a valid choice."
msgstr "Opción inválida"

#: src/wtforms/fields/core.py:573
msgid "Invalid choice(s): one or more data inputs could not be coerced."
msgstr ""
"Opción(es) inválida(s): una o más entradas de datos no pueden ser "
"ajustadas"

#: src/wtforms/fields/core.py:584
#, python-format
msgid "'%(value)s' is not a valid choice for this field."
msgstr "'%(value)s' no es una opción válida para este campo"

#: src/wtforms/fields/core.py:679 src/wtforms/fields/core.py:689
msgid "Not a valid integer value."
msgstr "No es un valor entero válido"

#: src/wtforms/fields/core.py:760
msgid "Not a valid decimal value."
msgstr "No es un numero decimal válido"

#: src/wtforms/fields/core.py:788
msgid "Not a valid float value."
msgstr "No es un número de punto flotante válido"

#: src/wtforms/fields/core.py:853
msgid "Not a valid datetime value."
msgstr ""

#: src/wtforms/fields/core.py:871
msgid "Not a valid date value."
msgstr ""

#: src/wtforms/fields/core.py:889
msgid "Not a valid time value."
msgstr ""
