# French translations for WTForms.
# Copyright (C) 2020 WTForms Team
# This file is distributed under the same license as the WTForms project.
# <AUTHOR> <EMAIL>, 2020.
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2020.
#
msgid ""
msgstr ""
"Project-Id-Version: WTForms 1.0.3\n"
"Report-Msgid-Bugs-To: <EMAIL>\n"
"POT-Creation-Date: 2020-04-25 11:34-0700\n"
"PO-Revision-Date: 2020-06-21 12:09+0200\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON>ult <<EMAIL>>\n"
"Language: fr\n"
"Language-Team: French\n"
"Plural-Forms: nplurals=2; plural=(n > 1)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.8.0\n"
"X-Generator: Gtranslator 3.36.0\n"

#: src/wtforms/validators.py:87
#, python-format
msgid "Invalid field name '%s'."
msgstr "Nom de champ non valide « %s »."

#: src/wtforms/validators.py:98
#, python-format
msgid "Field must be equal to %(other_name)s."
msgstr "Le champ doit être égal à %(other_name)s."

#: src/wtforms/validators.py:134
#, python-format
msgid "Field must be at least %(min)d character long."
msgid_plural "Field must be at least %(min)d characters long."
msgstr[0] "Le champ doit contenir au moins %(min)d caractère."
msgstr[1] "Le champ doit contenir au moins %(min)d caractères."

#: src/wtforms/validators.py:140
#, python-format
msgid "Field cannot be longer than %(max)d character."
msgid_plural "Field cannot be longer than %(max)d characters."
msgstr[0] "Le champ ne peut pas contenir plus de %(max)d caractère."
msgstr[1] "Le champ ne peut pas contenir plus de %(max)d caractères."

#: src/wtforms/validators.py:146
#, python-format
msgid "Field must be exactly %(max)d character long."
msgid_plural "Field must be exactly %(max)d characters long."
msgstr[0] "Le champ doit contenir exactement %(max)d caractère."
msgstr[1] "Le champ doit contenir exactement %(max)d caractères."

#: src/wtforms/validators.py:152
#, python-format
msgid "Field must be between %(min)d and %(max)d characters long."
msgstr ""
"La longueur du champ doit être comprise entre %(min)d et %(max)d caractères."

#: src/wtforms/validators.py:197
#, python-format
msgid "Number must be at least %(min)s."
msgstr "Le nombre doit être au minimum %(min)s."

#: src/wtforms/validators.py:199
#, python-format
msgid "Number must be at most %(max)s."
msgstr "Le nombre doit être au maximum %(max)s."

#: src/wtforms/validators.py:201
#, python-format
msgid "Number must be between %(min)s and %(max)s."
msgstr "Le nombre doit être compris entre %(min)s et %(max)s."

#: src/wtforms/validators.py:269 src/wtforms/validators.py:294
msgid "This field is required."
msgstr "Ce champ est requis."

#: src/wtforms/validators.py:327
msgid "Invalid input."
msgstr "Saisie non valide."

#: src/wtforms/validators.py:387
msgid "Invalid email address."
msgstr "Adresse électronique non valide."

#: src/wtforms/validators.py:423
msgid "Invalid IP address."
msgstr "Adresse IP non valide."

#: src/wtforms/validators.py:466
msgid "Invalid Mac address."
msgstr "Adresse MAC non valide."

#: src/wtforms/validators.py:501
msgid "Invalid URL."
msgstr "URL non valide."

#: src/wtforms/validators.py:522
msgid "Invalid UUID."
msgstr "UUID non valide."

#: src/wtforms/validators.py:553
#, python-format
msgid "Invalid value, must be one of: %(values)s."
msgstr "Valeur non valide, doit être parmi : %(values)s."

#: src/wtforms/validators.py:588
#, python-format
msgid "Invalid value, can't be any of: %(values)s."
msgstr "Valeur non valide, ne peut contenir : %(values)s."

#: src/wtforms/csrf/core.py:96
msgid "Invalid CSRF Token."
msgstr "Jeton CSRF non valide."

#: src/wtforms/csrf/session.py:63
msgid "CSRF token missing."
msgstr "Jeton CSRF manquant."

#: src/wtforms/csrf/session.py:71
msgid "CSRF failed."
msgstr "CSRF a échoué."

#: src/wtforms/csrf/session.py:76
msgid "CSRF token expired."
msgstr "Jeton CSRF expiré."

#: src/wtforms/fields/core.py:534
msgid "Invalid Choice: could not coerce."
msgstr "Choix non valide, ne peut pas être converti."

#: src/wtforms/fields/core.py:538
msgid "Choices cannot be None."
msgstr "Vous devez choisir au moins un élément."

#: src/wtforms/fields/core.py:545
msgid "Not a valid choice."
msgstr "N'est pas un choix valide."

#: src/wtforms/fields/core.py:573
msgid "Invalid choice(s): one or more data inputs could not be coerced."
msgstr ""
"Choix incorrect, une ou plusieurs saisies ne peuvent pas être converties."

#: src/wtforms/fields/core.py:584
#, python-format
msgid "'%(value)s' is not a valid choice for this field."
msgstr "« %(value)s » n'est pas un choix valide pour ce champ."

#: src/wtforms/fields/core.py:679 src/wtforms/fields/core.py:689
msgid "Not a valid integer value."
msgstr "N'est pas un entier valide."

#: src/wtforms/fields/core.py:760
msgid "Not a valid decimal value."
msgstr "N'est pas une valeur décimale valide."

#: src/wtforms/fields/core.py:788
msgid "Not a valid float value."
msgstr "N'est pas un flottant valide."

#: src/wtforms/fields/core.py:853
msgid "Not a valid datetime value."
msgstr "N'est pas une date/heure valide."

#: src/wtforms/fields/core.py:871
msgid "Not a valid date value."
msgstr "N'est pas une date valide."

#: src/wtforms/fields/core.py:889
msgid "Not a valid time value."
msgstr "N'est pas un horaire valide."
