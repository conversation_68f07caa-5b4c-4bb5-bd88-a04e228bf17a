# Russian translations for WTForms.
# Copyright (C) 2020 WTForms Team
# This file is distributed under the same license as the WTForms project.
# <AUTHOR> <EMAIL>, 2020.
#
msgid ""
msgstr ""
"Project-Id-Version: WTForms 1.0.3\n"
"Report-Msgid-Bugs-To: EMAIL@ADDRESS\n"
"POT-Creation-Date: 2020-04-25 11:34-0700\n"
"PO-Revision-Date: 2012-08-01 10:23+0400\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>\n"
"Language: ru\n"
"Language-Team: ru <<EMAIL>>\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && "
"n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.8.0\n"

#: src/wtforms/validators.py:87
#, python-format
msgid "Invalid field name '%s'."
msgstr "Неправильное имя поля '%s'."

#: src/wtforms/validators.py:98
#, python-format
msgid "Field must be equal to %(other_name)s."
msgstr "Поле должно совпадать с %(other_name)s."

#: src/wtforms/validators.py:134
#, python-format
msgid "Field must be at least %(min)d character long."
msgid_plural "Field must be at least %(min)d characters long."
msgstr[0] "Значение должно содержать не менее %(min)d символа."
msgstr[1] "Значение должно содержать не менее %(min)d символов."
msgstr[2] "Значение должно содержать не менее %(min)d символов."

#: src/wtforms/validators.py:140
#, python-format
msgid "Field cannot be longer than %(max)d character."
msgid_plural "Field cannot be longer than %(max)d characters."
msgstr[0] "Значение не должно содержать более %(max)d символа."
msgstr[1] "Значение не должно содержать более %(max)d символов."
msgstr[2] "Значение не должно содержать более %(max)d символов."

#: src/wtforms/validators.py:146
#, python-format
msgid "Field must be exactly %(max)d character long."
msgid_plural "Field must be exactly %(max)d characters long."
msgstr[0] "Значение должно быть точно %(max)d символа."
msgstr[1] "Значение должно быть %(max)d символов."
msgstr[2] "Значение должно быть %(max)d символов."

#: src/wtforms/validators.py:152
#, python-format
msgid "Field must be between %(min)d and %(max)d characters long."
msgstr "Значение должно содержать от %(min)d до %(max)d символов."

#: src/wtforms/validators.py:197
#, python-format
msgid "Number must be at least %(min)s."
msgstr "Число должно быть больше %(min)s."

#: src/wtforms/validators.py:199
#, python-format
msgid "Number must be at most %(max)s."
msgstr "Число должно быть меньше %(max)s."

#: src/wtforms/validators.py:201
#, python-format
msgid "Number must be between %(min)s and %(max)s."
msgstr "Значение должно быть между %(min)s и %(max)s."

#: src/wtforms/validators.py:269 src/wtforms/validators.py:294
msgid "This field is required."
msgstr "Обязательное поле."

#: src/wtforms/validators.py:327
msgid "Invalid input."
msgstr "Некорректный ввод."

#: src/wtforms/validators.py:387
msgid "Invalid email address."
msgstr "Неверный адрес электронной почты."

#: src/wtforms/validators.py:423
msgid "Invalid IP address."
msgstr "Неверный IP адрес."

#: src/wtforms/validators.py:466
msgid "Invalid Mac address."
msgstr "Неверный MAC адрес."

#: src/wtforms/validators.py:501
msgid "Invalid URL."
msgstr "Неверный URL."

#: src/wtforms/validators.py:522
msgid "Invalid UUID."
msgstr "Неверный UUID."

#: src/wtforms/validators.py:553
#, python-format
msgid "Invalid value, must be one of: %(values)s."
msgstr "Неверное значение, должно быть одним из %(values)s."

#: src/wtforms/validators.py:588
#, python-format
msgid "Invalid value, can't be any of: %(values)s."
msgstr "Неверное значение, не должно быть одним из %(values)s."

#: src/wtforms/csrf/core.py:96
msgid "Invalid CSRF Token."
msgstr "Неверный CSRF токен."

#: src/wtforms/csrf/session.py:63
msgid "CSRF token missing."
msgstr "CSRF токен отсутствует."

#: src/wtforms/csrf/session.py:71
msgid "CSRF failed."
msgstr "Ошибка CSRF."

#: src/wtforms/csrf/session.py:76
msgid "CSRF token expired."
msgstr "CSRF токен просрочен."

#: src/wtforms/fields/core.py:534
msgid "Invalid Choice: could not coerce."
msgstr "Неверный вариант: невозможно преобразовать."

#: src/wtforms/fields/core.py:538
msgid "Choices cannot be None."
msgstr "Выбор не может быть None"

#: src/wtforms/fields/core.py:545
msgid "Not a valid choice."
msgstr "Неверный вариант."

#: src/wtforms/fields/core.py:573
msgid "Invalid choice(s): one or more data inputs could not be coerced."
msgstr ""
"Неверный вариант(варианты): одно или несколько значений невозможно "
"преобразовать."

#: src/wtforms/fields/core.py:584
#, python-format
msgid "'%(value)s' is not a valid choice for this field."
msgstr "'%(value)s' - неверный вариант для этого поля."

#: src/wtforms/fields/core.py:679 src/wtforms/fields/core.py:689
msgid "Not a valid integer value."
msgstr "Неверное целое число."

#: src/wtforms/fields/core.py:760
msgid "Not a valid decimal value."
msgstr "Неверное десятичное число."

#: src/wtforms/fields/core.py:788
msgid "Not a valid float value."
msgstr "Неверное десятичное число."

#: src/wtforms/fields/core.py:853
msgid "Not a valid datetime value."
msgstr "Неверное значение даты и времени."

#: src/wtforms/fields/core.py:871
msgid "Not a valid date value."
msgstr "Неверное значение даты."

#: src/wtforms/fields/core.py:889
msgid "Not a valid time value."
msgstr "Неверное значение времени."
