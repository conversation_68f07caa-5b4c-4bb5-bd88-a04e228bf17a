# تطبيق إدارة المهام - Monday.com Clone

تطبيق ويب متكامل لإدارة المهام والمشاريع مشابه لـ Monday.com، مطور باستخدام Flask مع واجهة باللغة العربية.

## المميزات الرئيسية

### 🔐 نظام المصادقة
- تسجيل الدخول والخروج
- إنشاء حسابات جديدة
- حماية الصفحات بنظام المصادقة

### 📋 إدارة اللوحات
- إنشاء لوحات متعددة لتنظيم المشاريع
- وصف تفصيلي لكل لوحة
- عرض إحصائيات اللوحة (عدد المهام والأعمدة)

### ✅ إدارة المهام
- إضافة مهام جديدة
- تحديد حالة المهمة (في الانتظار، قيد التنفيذ، مكتملة)
- تعيين المهام للمستخدمين
- تحرير وحذف المهام

### 📊 الأعمدة المخصصة
- إضافة أعمدة مخصصة بأنواع مختلفة:
  - **نص**: لإدخال النصوص
  - **رقم**: للأرقام والحسابات
  - **تاريخ**: لاختيار التواريخ
  - **حالة**: لحالة المهمة
  - **مستخدم**: لتعيين المستخدمين

### ⚡ التحرير المباشر
- تحرير الخلايا مباشرة بالنقر عليها
- حفظ تلقائي للتغييرات
- واجهة سهلة الاستخدام مشابهة لـ Monday.com

### 🎨 واجهة المستخدم
- تصميم عصري ومتجاوب
- دعم كامل للغة العربية (RTL)
- استخدام Bootstrap 5 RTL
- أيقونات Font Awesome
- تأثيرات حركية وتفاعلية

## التقنيات المستخدمة

### Backend
- **Flask**: إطار العمل الرئيسي
- **Flask-SQLAlchemy**: ORM لقاعدة البيانات
- **Flask-Login**: إدارة جلسات المستخدمين
- **Flask-Migrate**: إدارة هجرة قاعدة البيانات
- **Flask-WTF**: معالجة النماذج والحماية من CSRF
- **SQLite**: قاعدة البيانات

### Frontend
- **Bootstrap 5 RTL**: إطار العمل للتصميم
- **Font Awesome**: الأيقونات
- **JavaScript**: التفاعل والتحرير المباشر
- **CSS مخصص**: تحسينات التصميم

## هيكل المشروع

```
├── app.py                 # نقطة بدء التطبيق
├── config.py             # إعدادات التطبيق
├── extensions.py         # تهيئة الإضافات
├── models.py             # نماذج قاعدة البيانات
├── forms.py              # نماذج الويب
├── routes.py             # مسارات التطبيق
├── requirements.txt      # متطلبات Python
├── templates/            # قوالب HTML
│   ├── base.html
│   ├── login.html
│   ├── register.html
│   ├── dashboard.html
│   ├── board_detail.html
│   ├── create_board.html
│   ├── add_column.html
│   └── edit_task.html
└── static/              # الملفات الثابتة
    ├── css/
    │   └── style.css
    └── js/
        └── app.js
```

## التثبيت والتشغيل

### 1. تحميل المتطلبات
```bash
pip install -r requirements.txt
```

### 2. تشغيل التطبيق
```bash
python app.py
```

### 3. الوصول للتطبيق
افتح المتصفح وانتقل إلى: `http://localhost:5000`

## الاستخدام

### 1. إنشاء حساب
- انتقل إلى صفحة التسجيل
- أدخل اسم المستخدم والبريد الإلكتروني وكلمة المرور
- اضغط "إنشاء حساب"

### 2. إنشاء لوحة جديدة
- من لوحة التحكم، اضغط "إنشاء لوحة جديدة"
- أدخل عنوان ووصف اللوحة
- اضغط "إنشاء لوحة"

### 3. إضافة أعمدة مخصصة
- في صفحة اللوحة، اضغط "إضافة عمود"
- اختر اسم ونوع العمود
- اضغط "إضافة عمود"

### 4. إضافة مهام
- استخدم النموذج في أعلى صفحة اللوحة
- املأ تفاصيل المهمة
- اضغط "إضافة مهمة"

### 5. التحرير المباشر
- انقر على أي خلية في الجدول لتحريرها
- اضغط Enter أو انقر خارج الخلية للحفظ
- اضغط Escape للإلغاء

## المميزات المتقدمة

### الاختصارات
- **Ctrl + N**: إنشاء مهمة جديدة
- **Escape**: إغلاق النماذج المفتوحة

### التحقق من النماذج
- التحقق من صحة البيانات المدخلة
- رسائل خطأ واضحة باللغة العربية
- منع إرسال النماذج الفارغة

### الأمان
- حماية من CSRF
- تشفير كلمات المرور
- حماية المسارات بنظام المصادقة

## التطوير المستقبلي

- [ ] إضافة نظام الإشعارات
- [ ] تصدير البيانات (Excel, PDF)
- [ ] نظام التعليقات على المهام
- [ ] رفع الملفات والمرفقات
- [ ] إحصائيات وتقارير متقدمة
- [ ] API للتكامل مع تطبيقات أخرى

## المساهمة

نرحب بالمساهمات! يرجى:
1. عمل Fork للمشروع
2. إنشاء فرع جديد للميزة
3. إجراء التغييرات المطلوبة
4. إرسال Pull Request

## الترخيص

هذا المشروع مرخص تحت رخصة MIT.

## الدعم

للحصول على الدعم أو الإبلاغ عن مشاكل، يرجى إنشاء Issue في المستودع.

---

**تم تطوير هذا المشروع بواسطة Augment Agent** 🚀
