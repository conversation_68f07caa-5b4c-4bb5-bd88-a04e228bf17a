# Application de Gestion des Tâches - Clone Monday.com

Application web complète de gestion des tâches et projets similaire à Monday.com, développée avec Flask et interface en français.

## Fonctionnalités Principales

### 🔐 Système d'Authentification
- Connexion et déconnexion
- Création de nouveaux comptes
- Protection des pages par authentification

### 📋 Gestion des Tableaux
- Création de tableaux multiples pour organiser les projets
- Description détaillée pour chaque tableau
- Affichage des statistiques du tableau (nombre de tâches et colonnes)

### ✅ Gestion des Tâches
- Ajout de nouvelles tâches
- Définition du statut de la tâche (En attente, En cours, Terminé)
- Attribution des tâches aux utilisateurs
- Modification et suppression des tâches

### 📊 Colonnes Personnalisées
- Ajout de colonnes personnalisées avec différents types :
  - **Texte** : pour saisir du texte
  - **Nombre** : pour les chiffres et calculs
  - **Date** : pour choisir des dates
  - **Statut** : pour l'état de la tâche
  - **Utilisateur** : pour assigner des utilisateurs

### ⚡ Édition en Ligne
- Modification des cellules directement en cliquant dessus
- Sauvegarde automatique des modifications
- Interface facile à utiliser similaire à Monday.com

### 🎨 Interface Utilisateur
- Design moderne et responsive
- Support complet du français (LTR)
- Utilisation de Bootstrap 5
- Icônes Font Awesome
- Effets d'animation et interactifs

## Technologies Utilisées

### Backend
- **Flask** : Framework principal
- **Flask-SQLAlchemy** : ORM pour la base de données
- **Flask-Login** : Gestion des sessions utilisateur
- **Flask-Migrate** : Gestion des migrations de base de données
- **Flask-WTF** : Traitement des formulaires et protection CSRF
- **SQLite** : Base de données

### Frontend
- **Bootstrap 5** : Framework de design
- **Font Awesome** : Icônes
- **JavaScript** : Interaction et édition en ligne
- **CSS personnalisé** : Améliorations du design

## Structure du Projet

```
├── app.py                 # Point d'entrée de l'application
├── config.py             # Configuration de l'application
├── extensions.py         # Initialisation des extensions
├── models.py             # Modèles de base de données
├── forms.py              # Formulaires web
├── routes.py             # Routes de l'application
├── requirements.txt      # Dépendances Python
├── templates/            # Templates HTML
│   ├── base.html
│   ├── login.html
│   ├── register.html
│   ├── dashboard.html
│   ├── board_detail.html
│   ├── create_board.html
│   ├── add_column.html
│   └── edit_task.html
└── static/              # Fichiers statiques
    ├── css/
    │   └── style.css
    └── js/
        └── app.js
```

## Installation et Exécution

### 1. Installer les dépendances
```bash
pip install -r requirements.txt
```

### 2. Lancer l'application
```bash
python app.py
```

### 3. Accéder à l'application
Ouvrez votre navigateur et allez à : `http://localhost:5000`

## Utilisation

### 1. Créer un compte
- Allez à la page d'inscription
- Entrez nom d'utilisateur, email et mot de passe
- Cliquez sur "Créer un compte"

### 2. Créer un nouveau tableau
- Depuis le tableau de bord, cliquez "Créer un nouveau tableau"
- Entrez le titre et la description du tableau
- Cliquez sur "Créer le tableau"

### 3. Ajouter des colonnes personnalisées
- Dans la page du tableau, cliquez "Ajouter une colonne"
- Choisissez le nom et le type de colonne
- Cliquez sur "Ajouter la colonne"

### 4. Ajouter des tâches
- Utilisez le formulaire en haut de la page du tableau
- Remplissez les détails de la tâche
- Cliquez sur "Ajouter la tâche"

### 5. Édition en ligne
- Cliquez sur n'importe quelle cellule du tableau pour la modifier
- Appuyez sur Entrée ou cliquez en dehors de la cellule pour sauvegarder
- Appuyez sur Échap pour annuler

## Fonctionnalités Avancées

### Raccourcis clavier
- **Ctrl + N** : Créer une nouvelle tâche
- **Échap** : Fermer les formulaires ouverts

### Validation des formulaires
- Vérification de la validité des données saisies
- Messages d'erreur clairs en français
- Prévention de l'envoi de formulaires vides

### Sécurité
- Protection contre CSRF
- Chiffrement des mots de passe
- Protection des routes par authentification

## Développement Futur

- [ ] Ajouter un système de notifications
- [ ] Export de données (Excel, PDF)
- [ ] Système de commentaires sur les tâches
- [ ] Upload de fichiers et pièces jointes
- [ ] Statistiques et rapports avancés
- [ ] API pour intégration avec d'autres applications

## Contribution

Les contributions sont les bienvenues ! Veuillez :
1. Faire un Fork du projet
2. Créer une nouvelle branche pour la fonctionnalité
3. Effectuer les modifications nécessaires
4. Envoyer une Pull Request

## Licence

Ce projet est sous licence MIT.

## Support

Pour obtenir de l'aide ou signaler des problèmes, veuillez créer une Issue dans le dépôt.

---

**Ce projet a été développé par Augment Agent** 🚀
