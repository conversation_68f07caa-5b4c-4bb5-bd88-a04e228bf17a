from flask import Flask, render_template
from config import Config
from extensions import db, migrate, login_manager
from flask_login import current_user
from models import User
from routes import main_bp
import os
import logging

def create_app():
    app = Flask(__name__)
    app.config.from_object(Config)

    # C<PERSON>er le dossier uploads s'il n'existe pas
    if not os.path.exists(app.config['UPLOAD_FOLDER']):
        os.makedirs(app.config['UPLOAD_FOLDER'])

    # Initialiser les extensions
    db.init_app(app)
    migrate.init_app(app, db)
    login_manager.init_app(app)

    @login_manager.user_loader
    def load_user(user_id):
        return User.query.get(int(user_id))

    # Enregistrer le blueprint
    app.register_blueprint(main_bp)

    # Ajouter les variables de contexte
    @app.context_processor
    def inject_user():
        return dict(current_user=current_user)

    # Gestion des erreurs
    @app.errorhandler(500)
    def internal_error(error):
        db.session.rollback()
        return render_template('error.html', error="Erreur interne du serveur"), 500

    @app.errorhandler(404)
    def not_found_error(error):
        return render_template('error.html', error="Page non trouvée"), 404

    return app

if __name__ == '__main__':
    try:
        print("🔄 Démarrage de l'application...")
        app = create_app()
        print("✅ Application créée avec succès")

        # Créer les tables si elles n'existent pas
        with app.app_context():
            print("🔄 Création de la base de données...")
            db.create_all()
            print("✅ Base de données créée avec succès")

        print("🚀 Application de gestion des tâches démarrée avec succès !")
        print("📱 Vous pouvez accéder à l'application sur : http://localhost:5000")
        print("🔧 Pour arrêter : appuyez sur Ctrl+C")

        # Lancer l'application
        app.run(debug=True, host='127.0.0.1', port=5000, use_reloader=False)

    except Exception as e:
        print(f"❌ Erreur lors du démarrage de l'application : {e}")
        import traceback
        traceback.print_exc()
        input("Appuyez sur Entrée pour quitter...")
