from flask import Flask
from config import Config
from extensions import db, migrate, login_manager
from flask_login import current_user
from models import User
from routes import main_bp

def create_app():
    app = Flask(__name__)
    app.config.from_object(Config)

    db.init_app(app)
    migrate.init_app(app, db)
    login_manager.init_app(app)

    @login_manager.user_loader
    def load_user(user_id):
        return User.query.get(int(user_id))

    app.register_blueprint(main_bp)

    @app.context_processor
    def inject_user():
        return dict(current_user=current_user)

    return app

if __name__ == '__main__':
    app = create_app()
    app.run(debug=True)
# app.py - نقطة بدء المشروع
