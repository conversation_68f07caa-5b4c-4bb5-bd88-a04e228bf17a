from flask import Flask, render_template
from config import Config
from extensions import db, migrate, login_manager
from flask_login import current_user
from models import User
from routes import main_bp
import os
import logging

def create_app():
    app = Flask(__name__)
    app.config.from_object(Config)

    # إنشاء مجلد uploads إذا لم يكن موجوداً
    if not os.path.exists(app.config['UPLOAD_FOLDER']):
        os.makedirs(app.config['UPLOAD_FOLDER'])

    # تهيئة الإضافات
    db.init_app(app)
    migrate.init_app(app, db)
    login_manager.init_app(app)

    @login_manager.user_loader
    def load_user(user_id):
        return User.query.get(int(user_id))

    # تسجيل البلوبرينت
    app.register_blueprint(main_bp)

    # إضافة متغيرات السياق
    @app.context_processor
    def inject_user():
        return dict(current_user=current_user)

    # معالجة الأخطاء
    @app.errorhandler(500)
    def internal_error(error):
        db.session.rollback()
        return render_template('error.html', error="خطأ داخلي في الخادم"), 500

    @app.errorhandler(404)
    def not_found_error(error):
        return render_template('error.html', error="الصفحة غير موجودة"), 404

    return app

if __name__ == '__main__':
    try:
        print("🔄 بدء تشغيل التطبيق...")
        app = create_app()
        print("✅ تم إنشاء التطبيق بنجاح")

        # إنشاء الجداول إذا لم تكن موجودة
        with app.app_context():
            print("🔄 إنشاء قاعدة البيانات...")
            db.create_all()
            print("✅ تم إنشاء قاعدة البيانات بنجاح")

        print("🚀 تم تشغيل تطبيق إدارة المهام بنجاح!")
        print("📱 يمكنك الوصول للتطبيق على: http://localhost:5000")
        print("🔧 للإيقاف: اضغط Ctrl+C")

        # تشغيل التطبيق
        app.run(debug=True, host='127.0.0.1', port=5000, use_reloader=False)

    except Exception as e:
        print(f"❌ خطأ في تشغيل التطبيق: {e}")
        import traceback
        traceback.print_exc()
        input("اضغط Enter للخروج...")
