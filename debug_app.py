#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import traceback

def test_imports():
    """اختبار جميع الاستيرادات"""
    try:
        print("🔍 اختبار الاستيرادات...")
        
        print("- اختبار Flask...")
        from flask import Flask
        print("✅ Flask")
        
        print("- اختبار config...")
        from config import Config
        print("✅ Config")
        
        print("- اختبار extensions...")
        from extensions import db, migrate, login_manager
        print("✅ Extensions")
        
        print("- اختبار models...")
        from models import User
        print("✅ Models")
        
        print("- اختبار routes...")
        from routes import main_bp
        print("✅ Routes")
        
        print("✅ جميع الاستيرادات نجحت!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في الاستيراد: {e}")
        traceback.print_exc()
        return False

def test_app_creation():
    """اختبار إنشاء التطبيق"""
    try:
        print("\n🔍 اختبار إنشاء التطبيق...")
        
        from app import create_app
        app = create_app()
        print("✅ تم إنشاء التطبيق بنجاح")
        
        with app.app_context():
            from extensions import db
            db.create_all()
            print("✅ تم إنشاء قاعدة البيانات بنجاح")
            
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء التطبيق: {e}")
        traceback.print_exc()
        return False

def test_routes():
    """اختبار المسارات"""
    try:
        print("\n🔍 اختبار المسارات...")
        
        from app import create_app
        app = create_app()
        
        with app.test_client() as client:
            # اختبار الصفحة الرئيسية
            response = client.get('/')
            print(f"📊 الصفحة الرئيسية: {response.status_code}")
            
            # اختبار صفحة تسجيل الدخول
            response = client.get('/login')
            print(f"📊 صفحة تسجيل الدخول: {response.status_code}")
            
            if response.status_code == 200:
                print("✅ المسارات تعمل بشكل صحيح")
                return True
            else:
                print(f"❌ خطأ في المسارات: {response.status_code}")
                print(f"Response data: {response.data.decode()}")
                return False
                
    except Exception as e:
        print(f"❌ خطأ في اختبار المسارات: {e}")
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 بدء اختبار التطبيق...")
    
    # اختبار الاستيرادات
    if not test_imports():
        print("❌ فشل في اختبار الاستيرادات")
        sys.exit(1)
    
    # اختبار إنشاء التطبيق
    if not test_app_creation():
        print("❌ فشل في اختبار إنشاء التطبيق")
        sys.exit(1)
    
    # اختبار المسارات
    if not test_routes():
        print("❌ فشل في اختبار المسارات")
        sys.exit(1)
    
    print("\n🎉 جميع الاختبارات نجحت! التطبيق جاهز للعمل.")
