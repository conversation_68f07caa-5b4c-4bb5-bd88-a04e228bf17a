#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import traceback

def test_imports():
    """Test de tous les imports"""
    try:
        print("🔍 Test des imports...")

        print("- Test Flask...")
        from flask import Flask
        print("✅ Flask")

        print("- Test config...")
        from config import Config
        print("✅ Config")

        print("- Test extensions...")
        from extensions import db, migrate, login_manager
        print("✅ Extensions")

        print("- Test models...")
        from models import User
        print("✅ Models")

        print("- Test routes...")
        from routes import main_bp
        print("✅ Routes")

        print("✅ Tous les imports ont réussi !")
        return True

    except Exception as e:
        print(f"❌ Erreur d'import : {e}")
        traceback.print_exc()
        return False

def test_app_creation():
    """Test de création de l'application"""
    try:
        print("\n🔍 Test de création de l'application...")

        from app import create_app
        app = create_app()
        print("✅ Application créée avec succès")

        with app.app_context():
            from extensions import db
            db.create_all()
            print("✅ Base de données créée avec succès")

        return True

    except Exception as e:
        print(f"❌ Erreur de création de l'application : {e}")
        traceback.print_exc()
        return False

def test_routes():
    """Test des routes"""
    try:
        print("\n🔍 Test des routes...")

        from app import create_app
        app = create_app()

        with app.test_client() as client:
            # Test de la page d'accueil
            response = client.get('/')
            print(f"📊 Page d'accueil : {response.status_code}")

            # Test de la page de connexion
            response = client.get('/login')
            print(f"📊 Page de connexion : {response.status_code}")

            if response.status_code == 200:
                print("✅ Les routes fonctionnent correctement")
                return True
            else:
                print(f"❌ Erreur dans les routes : {response.status_code}")
                print(f"Response data: {response.data.decode()}")
                return False

    except Exception as e:
        print(f"❌ Erreur de test des routes : {e}")
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 Début du test de l'application...")

    # Test des imports
    if not test_imports():
        print("❌ Échec du test des imports")
        sys.exit(1)

    # Test de création de l'application
    if not test_app_creation():
        print("❌ Échec du test de création de l'application")
        sys.exit(1)

    # Test des routes
    if not test_routes():
        print("❌ Échec du test des routes")
        sys.exit(1)

    print("\n🎉 Tous les tests ont réussi ! L'application est prête à fonctionner.")
