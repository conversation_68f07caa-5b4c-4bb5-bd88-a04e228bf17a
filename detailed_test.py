#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import time

def test_detailed():
    """Test détaillé de l'application"""

    print("🔍 Test détaillé de l'application...")
    time.sleep(2)

    try:
        response = requests.get('http://127.0.0.1:5000', timeout=10)

        print(f"📊 Code de réponse : {response.status_code}")
        print(f"📏 Taille du contenu : {len(response.content)} octets")
        print(f"📋 Headers : {dict(response.headers)}")

        if response.status_code == 500:
            print("❌ Erreur 500 - Erreur interne du serveur")
            print("📄 Contenu de la réponse :")
            print(response.text)
        elif response.status_code == 200:
            print("✅ L'application fonctionne correctement !")
        elif response.status_code == 302:
            print("✅ Redirection - L'application fonctionne")
            print(f"🔗 Redirection vers : {response.headers.get('Location', 'non spécifié')}")
        else:
            print(f"⚠️ Code de réponse inattendu : {response.status_code}")

    except requests.exceptions.ConnectionError:
        print("❌ Impossible de se connecter à l'application")
    except Exception as e:
        print(f"❌ Erreur : {e}")

if __name__ == "__main__":
    test_detailed()
