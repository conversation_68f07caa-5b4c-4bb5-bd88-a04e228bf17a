#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import time

def test_detailed():
    """اختبار مفصل للتطبيق"""
    
    print("🔍 اختبار مفصل للتطبيق...")
    time.sleep(2)
    
    try:
        response = requests.get('http://127.0.0.1:5000', timeout=10)
        
        print(f"📊 كود الاستجابة: {response.status_code}")
        print(f"📏 حجم المحتوى: {len(response.content)} بايت")
        print(f"📋 Headers: {dict(response.headers)}")
        
        if response.status_code == 500:
            print("❌ خطأ 500 - خطأ داخلي في الخادم")
            print("📄 محتوى الاستجابة:")
            print(response.text)
        elif response.status_code == 200:
            print("✅ التطبيق يعمل بشكل صحيح!")
        elif response.status_code == 302:
            print("✅ إعادة توجيه - التطبيق يعمل")
            print(f"🔗 إعادة التوجيه إلى: {response.headers.get('Location', 'غير محدد')}")
        else:
            print(f"⚠️ كود استجابة غير متوقع: {response.status_code}")
            
    except requests.exceptions.ConnectionError:
        print("❌ لا يمكن الاتصال بالتطبيق")
    except Exception as e:
        print(f"❌ خطأ: {e}")

if __name__ == "__main__":
    test_detailed()
