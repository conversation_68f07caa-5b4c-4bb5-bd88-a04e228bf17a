from flask_wtf import F<PERSON>kForm
from wtforms import <PERSON>Field, PasswordField, SubmitField, SelectField, DateField
from wtforms.validators import InputRequired
from wtforms_sqlalchemy.fields import QuerySelectField
from models import User

def get_users():
    return User.query.all()

class LoginForm(FlaskForm):
    username = String<PERSON>ield("Nom d'utilisateur", validators=[InputRequired()])
    password = PasswordField("Mot de passe", validators=[InputRequired()])
    submit = SubmitField("Connexion")

class RegisterForm(FlaskForm):
    username = StringField("Nom d'utilisateur", validators=[InputRequired()])
    password = PasswordField("Mot de passe", validators=[InputRequired()])
    submit = SubmitField("S'inscrire")

class BoardForm(FlaskForm):
    title = StringField("Titre", validators=[InputRequired()])
    description = StringField("Description")
    submit = SubmitField("Créer")

class ColumnForm(FlaskForm):
    name = StringField("Nom de la colonne", validators=[InputRequired()])
    type = SelectField("Type", choices=[
        ("text", "Texte"),
        ("date", "Date"),
        ("status", "Statut"),
        ("user", "Personne")
    ])
    submit = SubmitField("Ajouter colonne")
# forms.py - نماذج WTForms
