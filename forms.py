from flask_wtf import FlaskForm
from wtforms import <PERSON><PERSON><PERSON>, PasswordField, SubmitField, SelectField, DateField, TextAreaField
from wtforms.validators import InputRequired, Length, Email, EqualTo
from models import User

def get_users():
    return User.query.all()

class LoginForm(FlaskForm):
    username = StringField("اسم المستخدم", validators=[InputRequired()])
    password = PasswordField("كلمة المرور", validators=[InputRequired()])
    submit = SubmitField("تسجيل الدخول")

class RegisterForm(FlaskForm):
    username = StringField("اسم المستخدم", validators=[InputRequired(), Length(min=4, max=20)])
    email = StringField("البريد الإلكتروني", validators=[InputRequired(), Email()])
    password = PasswordField("كلمة المرور", validators=[InputRequired(), Length(min=6)])
    password2 = PasswordField("تأكيد كلمة المرور", validators=[InputRequired(), EqualTo('password')])
    submit = SubmitField("إنشاء حساب")

class BoardForm(FlaskForm):
    title = StringField("عنوان اللوحة", validators=[InputRequired(), Length(min=1, max=100)])
    description = TextAreaField("الوصف")
    submit = SubmitField("إنشاء لوحة")

class TaskForm(FlaskForm):
    title = StringField("عنوان المهمة", validators=[InputRequired(), Length(min=1, max=100)])
    description = TextAreaField("الوصف")
    status = SelectField("الحالة", choices=[
        ('في الانتظار', 'في الانتظار'),
        ('قيد التنفيذ', 'قيد التنفيذ'),
        ('مكتملة', 'مكتملة')
    ])
    assigned_to = SelectField("مُكلف إلى", coerce=int)
    submit = SubmitField("إضافة مهمة")

class ColumnForm(FlaskForm):
    name = StringField("اسم العمود", validators=[InputRequired(), Length(min=1, max=100)])
    type = SelectField("نوع العمود", choices=[
        ("text", "نص"),
        ("number", "رقم"),
        ("date", "تاريخ"),
        ("status", "حالة"),
        ("user", "مستخدم")
    ])
    submit = SubmitField("إضافة عمود")
