from extensions import db
from flask_login import UserMixin

class User(UserMixin, db.Model):
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(150), unique=True, nullable=False)
    password_hash = db.Column(db.String(256), nullable=False)
    tasks = db.relationship('Task', backref='assigned_to', lazy=True)

class Board(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    title = db.Column(db.String(150), nullable=False)
    description = db.Column(db.Text)
    tasks = db.relationship('Task', backref='board', lazy=True)
    columns = db.relationship('Column', backref='board', lazy=True)

class Task(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    title = db.Column(db.String(150), nullable=False)
    description = db.Column(db.Text)
    status = db.Column(db.String(20), default="En cours")
    board_id = db.Column(db.Integer, db.ForeignKey('board.id'))
    assigned_to_id = db.Column(db.Integer, db.ForeignKey('user.id'))
    cells = db.relationship('Cell', backref='task', lazy=True)

class Column(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    type = db.Column(db.String(20), nullable=False)  # text, date, status, user
    board_id = db.Column(db.Integer, db.ForeignKey('board.id'))

class Cell(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    task_id = db.Column(db.Integer, db.ForeignKey('task.id'))
    column_id = db.Column(db.Integer, db.ForeignKey('column.id'))
    value = db.Column(db.String(200))
# models.py - يحتوي على نماذج SQLAlchemy
