#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from app import create_app
from extensions import db

def test_app_creation():
    """اختبار إنشاء التطبيق"""
    try:
        app = create_app()
        print("✅ تم إنشاء التطبيق بنجاح")
        
        with app.app_context():
            # اختبار قاعدة البيانات
            db.create_all()
            print("✅ تم إنشاء قاعدة البيانات بنجاح")
            
            # اختبار المسارات
            with app.test_client() as client:
                response = client.get('/')
                print(f"📊 استجابة الصفحة الرئيسية: {response.status_code}")
                
                if response.status_code == 302:  # إعادة توجيه
                    print("✅ إعادة التوجيه تعمل بشكل صحيح")
                    
                    # اختبار صفحة تسجيل الدخول
                    response = client.get('/login')
                    print(f"📊 استجابة صفحة تسجيل الدخول: {response.status_code}")
                    
                    if response.status_code == 200:
                        print("✅ صفحة تسجيل الدخول تعمل بشكل صحيح")
                        return True
                    else:
                        print("❌ مشكلة في صفحة تسجيل الدخول")
                        return False
                else:
                    print("❌ مشكلة في إعادة التوجيه")
                    return False
                    
    except Exception as e:
        print(f"❌ خطأ في اختبار التطبيق: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_app_creation()
    if success:
        print("\n🎉 جميع الاختبارات نجحت!")
    else:
        print("\n❌ فشل في الاختبارات")
