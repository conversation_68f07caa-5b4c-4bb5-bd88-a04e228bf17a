#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from app import create_app
from extensions import db

def test_app_creation():
    """Test de création de l'application"""
    try:
        app = create_app()
        print("✅ Application créée avec succès")

        with app.app_context():
            # Test de la base de données
            db.create_all()
            print("✅ Base de données créée avec succès")

            # Test des routes
            with app.test_client() as client:
                response = client.get('/')
                print(f"📊 Réponse de la page d'accueil : {response.status_code}")

                if response.status_code == 302:  # Redirection
                    print("✅ La redirection fonctionne correctement")

                    # Test de la page de connexion
                    response = client.get('/login')
                    print(f"📊 Réponse de la page de connexion : {response.status_code}")

                    if response.status_code == 200:
                        print("✅ La page de connexion fonctionne correctement")
                        return True
                    else:
                        print("❌ Problème avec la page de connexion")
                        return False
                else:
                    print("❌ Problème avec la redirection")
                    return False

    except Exception as e:
        print(f"❌ Erreur dans le test de l'application : {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_app_creation()
    if success:
        print("\n🎉 Tous les tests ont réussi !")
    else:
        print("\n❌ Échec des tests")
