/* Fichier de style personnalisé pour l'application de gestion des tâches */

:root {
    --primary-color: #0073ea;
    --secondary-color: #6c757d;
    --success-color: #28a745;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --info-color: #17a2b8;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: var(--light-color);
    line-height: 1.6;
}

/* Amélioration de la barre de navigation */
.navbar-brand {
    font-weight: bold;
    font-size: 1.5rem;
}

/* Amélioration des cartes */
.card {
    border: none;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    border-radius: 10px;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.2);
}

.card-header {
    border-radius: 10px 10px 0 0 !important;
    border-bottom: none;
    font-weight: bold;
}

/* Amélioration des tableaux */
.table {
    background-color: white;
    border-radius: 10px;
    overflow: hidden;
}

.table th {
    background-color: var(--light-color);
    border-top: none;
    font-weight: 600;
    color: var(--dark-color);
    padding: 15px;
}

.table td {
    padding: 12px 15px;
    vertical-align: middle;
}

/* Cellules éditables */
.editable-cell {
    cursor: pointer;
    padding: 8px 12px;
    border-radius: 6px;
    transition: all 0.2s ease;
    min-height: 40px;
    display: flex;
    align-items: center;
}

.editable-cell:hover {
    background-color: #e9ecef;
    transform: scale(1.02);
}

.editable-cell:focus {
    outline: 2px solid var(--primary-color);
    background-color: white;
}

/* Amélioration des boutons */
.btn {
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s ease;
    padding: 8px 16px;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.btn-group .btn {
    margin: 0 2px;
}

/* Amélioration des formulaires */
.form-control, .form-select {
    border-radius: 8px;
    border: 2px solid #e9ecef;
    transition: all 0.3s ease;
    padding: 10px 15px;
}

.form-control:focus, .form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(0, 115, 234, 0.25);
}

.form-label {
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 8px;
}

/* Amélioration des badges */
.badge {
    font-size: 0.85rem;
    padding: 6px 12px;
    border-radius: 20px;
    font-weight: 500;
}

/* Amélioration des alertes */
.alert {
    border: none;
    border-radius: 10px;
    padding: 15px 20px;
    margin-bottom: 20px;
}

/* Amélioration des icônes */
.fas, .far {
    margin-left: 5px;
}

/* Amélioration des pages vides */
.empty-state {
    text-align: center;
    padding: 60px 20px;
}

.empty-state i {
    font-size: 4rem;
    color: var(--secondary-color);
    margin-bottom: 20px;
}

.empty-state h4 {
    color: var(--secondary-color);
    margin-bottom: 10px;
}

.empty-state p {
    color: var(--secondary-color);
    margin-bottom: 30px;
}

/* Amélioration de la responsivité pour les petits écrans */
@media (max-width: 768px) {
    .container {
        padding: 0 10px;
    }
    
    .card {
        margin-bottom: 20px;
    }
    
    .table-responsive {
        border-radius: 10px;
    }
    
    .btn-group {
        flex-direction: column;
    }
    
    .btn-group .btn {
        margin: 2px 0;
        width: 100%;
    }
}

/* Amélioration des animations */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in {
    animation: fadeIn 0.5s ease-in-out;
}

/* Amélioration du défilement */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--light-color);
}

::-webkit-scrollbar-thumb {
    background: var(--secondary-color);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--dark-color);
}

/* Amélioration des états de chargement */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: white;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}
