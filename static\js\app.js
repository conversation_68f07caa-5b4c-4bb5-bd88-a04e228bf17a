// Fichier JavaScript principal pour l'application de gestion des tâches

document.addEventListener('DOMContentLoaded', function() {
    // Initialisation de l'application
    initializeApp();
});

function initializeApp() {
    // Ajouter les effets d'animation
    addAnimations();

    // Initialiser l'édition en ligne des cellules
    initializeInlineEditing();

    // Initialiser les alertes automatiques
    initializeAlerts();

    // Initialiser la validation des formulaires
    initializeFormValidation();

    // Initialiser les raccourcis clavier
    initializeKeyboardShortcuts();
}

// Ajouter les effets d'animation
function addAnimations() {
    // Ajouter l'effet fade-in aux éléments
    const elements = document.querySelectorAll('.card, .table, .alert');
    elements.forEach((element, index) => {
        element.style.opacity = '0';
        element.style.transform = 'translateY(20px)';

        setTimeout(() => {
            element.style.transition = 'all 0.5s ease';
            element.style.opacity = '1';
            element.style.transform = 'translateY(0)';
        }, index * 100);
    });
}

// Initialiser l'édition en ligne des cellules
function initializeInlineEditing() {
    const editableCells = document.querySelectorAll('.editable-cell');

    editableCells.forEach(cell => {
        cell.addEventListener('click', function() {
            if (this.querySelector('input') || this.querySelector('select')) {
                return; // Éviter l'édition multiple
            }

            const originalValue = this.textContent.trim();
            const cellId = this.dataset.cellId;
            const type = this.dataset.type;

            if (!cellId) return;

            // Créer l'élément d'entrée approprié
            const inputElement = createInputElement(type, originalValue);

            // Remplacer le contenu
            this.innerHTML = '';
            this.appendChild(inputElement);
            inputElement.focus();

            // Sauvegarder la valeur lors de la perte de focus ou appui sur Entrée
            const saveValue = () => {
                const newValue = inputElement.value;
                updateCellValue(cellId, newValue, this, originalValue);
            };

            inputElement.addEventListener('blur', saveValue);
            inputElement.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    saveValue();
                }
            });

            // Annuler l'édition lors de l'appui sur Échap
            inputElement.addEventListener('keydown', (e) => {
                if (e.key === 'Escape') {
                    this.textContent = originalValue;
                }
            });
        });
    });
}

// Créer l'élément d'entrée approprié
function createInputElement(type, value) {
    let input;

    switch (type) {
        case 'status':
            input = document.createElement('select');
            input.className = 'form-select form-select-sm';
            const statusOptions = ['En attente', 'En cours', 'Terminé'];
            statusOptions.forEach(option => {
                const optionElement = document.createElement('option');
                optionElement.value = option;
                optionElement.textContent = option;
                if (option === value) optionElement.selected = true;
                input.appendChild(optionElement);
            });
            break;

        case 'date':
            input = document.createElement('input');
            input.type = 'date';
            input.className = 'form-control form-control-sm';
            input.value = value !== '-' ? value : '';
            break;

        case 'number':
            input = document.createElement('input');
            input.type = 'number';
            input.className = 'form-control form-control-sm';
            input.value = value !== '-' ? value : '';
            break;

        default:
            input = document.createElement('input');
            input.type = 'text';
            input.className = 'form-control form-control-sm';
            input.value = value !== '-' ? value : '';
    }

    return input;
}

// Mettre à jour la valeur de la cellule
function updateCellValue(cellId, newValue, cellElement, originalValue) {
    // Afficher l'indicateur de chargement
    cellElement.innerHTML = '<div class="spinner"></div>';

    fetch('/update_cell', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            cell_id: cellId,
            value: newValue
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            cellElement.textContent = data.value || '-';
            showNotification('Mise à jour réussie', 'success');
        } else {
            cellElement.textContent = originalValue;
            showNotification('Erreur de mise à jour', 'error');
        }
    })
    .catch(error => {
        cellElement.textContent = originalValue;
        showNotification('Erreur de connexion', 'error');
        console.error('Error:', error);
    });
}

// Afficher les notifications
function showNotification(message, type = 'info') {
    const alertClass = type === 'success' ? 'alert-success' :
                      type === 'error' ? 'alert-danger' : 'alert-info';

    const alertElement = document.createElement('div');
    alertElement.className = `alert ${alertClass} alert-dismissible fade show position-fixed`;
    alertElement.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    alertElement.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    document.body.appendChild(alertElement);

    // Supprimer automatiquement la notification après 3 secondes
    setTimeout(() => {
        if (alertElement.parentNode) {
            alertElement.remove();
        }
    }, 3000);
}

// Initialiser les alertes automatiques
function initializeAlerts() {
    const alerts = document.querySelectorAll('.alert');
    alerts.forEach(alert => {
        setTimeout(() => {
            if (alert.parentNode && !alert.querySelector('.btn-close:hover')) {
                alert.style.transition = 'opacity 0.5s ease';
                alert.style.opacity = '0';
                setTimeout(() => {
                    if (alert.parentNode) {
                        alert.remove();
                    }
                }, 500);
            }
        }, 5000);
    });
}

// Initialiser la validation des formulaires
function initializeFormValidation() {
    const forms = document.querySelectorAll('form');
    forms.forEach(form => {
        form.addEventListener('submit', function(e) {
            const requiredFields = this.querySelectorAll('[required]');
            let isValid = true;

            requiredFields.forEach(field => {
                if (!field.value.trim()) {
                    field.classList.add('is-invalid');
                    isValid = false;
                } else {
                    field.classList.remove('is-invalid');
                }
            });

            if (!isValid) {
                e.preventDefault();
                showNotification('Veuillez remplir tous les champs requis', 'error');
            }
        });
    });
}

// Initialiser les raccourcis clavier
function initializeKeyboardShortcuts() {
    document.addEventListener('keydown', function(e) {
        // Ctrl + N pour créer une nouvelle tâche
        if (e.ctrlKey && e.key === 'n') {
            e.preventDefault();
            const newTaskButton = document.querySelector('[href*="create"]');
            if (newTaskButton) {
                newTaskButton.click();
            }
        }

        // Échap pour fermer les formulaires ouverts
        if (e.key === 'Escape') {
            const modals = document.querySelectorAll('.modal.show');
            modals.forEach(modal => {
                const closeButton = modal.querySelector('.btn-close');
                if (closeButton) {
                    closeButton.click();
                }
            });
        }
    });
}

// Améliorer l'expérience utilisateur
function enhanceUserExperience() {
    // Ajouter des effets hover aux cartes
    const cards = document.querySelectorAll('.card');
    cards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-5px)';
        });

        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
        });
    });

    // Améliorer les boutons
    const buttons = document.querySelectorAll('.btn');
    buttons.forEach(button => {
        button.addEventListener('click', function() {
            this.style.transform = 'scale(0.95)';
            setTimeout(() => {
                this.style.transform = 'scale(1)';
            }, 150);
        });
    });
}

// Initialiser les améliorations lors du chargement de la page
document.addEventListener('DOMContentLoaded', enhanceUserExperience);
