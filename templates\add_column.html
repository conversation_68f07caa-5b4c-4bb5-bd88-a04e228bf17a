{% extends "base.html" %}
{% block title %}Ajouter une nouvelle colonne{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-8 col-lg-6">
        <div class="card">
            <div class="card-header bg-success text-white">
                <h4 class="mb-0">
                    <i class="fas fa-columns me-2"></i>Ajouter une nouvelle colonne
                </h4>
                <small>Pour le tableau : {{ board.title }}</small>
            </div>
            <div class="card-body">
                <form method="POST">
                    {{ form.hidden_tag() }}
                    
                    <div class="mb-3">
                        {{ form.name.label(class="form-label") }}
                        {{ form.name(class="form-control", placeholder="Entrez le nom de la colonne") }}
                        {% if form.name.errors %}
                            <div class="text-danger">
                                {% for error in form.name.errors %}
                                    <small>{{ error }}</small>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="mb-3">
                        {{ form.type.label(class="form-label") }}
                        {{ form.type(class="form-select") }}
                        {% if form.type.errors %}
                            <div class="text-danger">
                                {% for error in form.type.errors %}
                                    <small>{{ error }}</small>
                                {% endfor %}
                            </div>
                        {% endif %}
                        <div class="form-text">
                            <strong>Types de colonnes :</strong><br>
                            <i class="fas fa-font me-1"></i><strong>Texte :</strong> pour saisir du texte<br>
                            <i class="fas fa-hashtag me-1"></i><strong>Nombre :</strong> pour les chiffres et calculs<br>
                            <i class="fas fa-calendar me-1"></i><strong>Date :</strong> pour choisir des dates<br>
                            <i class="fas fa-flag me-1"></i><strong>Statut :</strong> pour l'état de la tâche<br>
                            <i class="fas fa-user me-1"></i><strong>Utilisateur :</strong> pour assigner des utilisateurs
                        </div>
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <a href="{{ url_for('main.board_detail', board_id=board.id) }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-2"></i>Annuler
                        </a>
                        {{ form.submit(class="btn btn-success") }}
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}
