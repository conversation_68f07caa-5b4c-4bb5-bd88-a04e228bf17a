<!DOCTYPE html>
<html lang="fr" dir="ltr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Gestion des Tâches - Monday Clone{% endblock %}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="{{ url_for('static', filename='css/style.css') }}" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
        }
        .navbar-brand {
            font-weight: bold;
            color: #0073ea !important;
        }
        .card {
            border: none;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            transition: transform 0.2s;
        }
        .card:hover {
            transform: translateY(-2px);
        }
        .table th {
            background-color: #f8f9fa;
            border-top: none;
        }
        .editable-cell {
            cursor: pointer;
            padding: 8px;
            border-radius: 4px;
            transition: background-color 0.2s;
        }
        .editable-cell:hover {
            background-color: #e9ecef;
        }
    </style>
    {% block head %}{% endblock %}
</head>
<body>
<nav class="navbar navbar-expand-lg navbar-dark bg-primary mb-4">
  <div class="container">
    <a class="navbar-brand" href="{{ url_for('main.dashboard') if current_user.is_authenticated else url_for('main.index') }}">
        <i class="fas fa-tasks me-2"></i>Gestion des Tâches
    </a>
    <div class="navbar-nav ms-auto">
      {% if current_user.is_authenticated %}
        <span class="navbar-text me-3">Bonjour, {{ current_user.username }}</span>
        <a href="{{ url_for('main.logout') }}" class="btn btn-outline-light btn-sm">
            <i class="fas fa-sign-out-alt me-1"></i>Se déconnecter
        </a>
      {% else %}
        <a href="{{ url_for('main.login') }}" class="btn btn-outline-light btn-sm me-2">
            <i class="fas fa-sign-in-alt me-1"></i>Se connecter
        </a>
        <a href="{{ url_for('main.register') }}" class="btn btn-light btn-sm">
            <i class="fas fa-user-plus me-1"></i>Créer un compte
        </a>
      {% endif %}
    </div>
  </div>
</nav>

<div class="container">
    {% with messages = get_flashed_messages() %}
      {% if messages %}
        {% for msg in messages %}
          <div class="alert alert-info alert-dismissible fade show" role="alert">
            {{ msg }}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
          </div>
        {% endfor %}
      {% endif %}
    {% endwith %}

    {% block content %}{% endblock %}
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
<script src="{{ url_for('static', filename='js/app.js') }}"></script>
{% block scripts %}{% endblock %}
</body>
</html>