<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <title>{% block title %}Monday Clone{% endblock %}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    {% block head %}{% endblock %}
</head>
<body>
<nav class="navbar navbar-expand-lg navbar-light bg-light mb-3">
  <div class="container">
    <a class="navbar-brand" href="{{ url_for('main.dashboard') }}">Monday Clone</a>
    <div>
      {% if current_user.is_authenticated %}
        <span class="me-3">Bonjour, {{ current_user.username }}</span>
        <a href="{{ url_for('main.logout') }}" class="btn btn-outline-danger btn-sm">Déconnexion</a>
      {% else %}
        <a href="{{ url_for('main.login') }}" class="btn btn-outline-primary btn-sm">Connexion</a>
      {% endif %}
    </div>
  </div>
</nav>

<div class="container">
    {% with messages = get_flashed_messages() %}
      {% if messages %}
        {% for msg in messages %}
          <div class="alert alert-info">{{ msg }}</div>
        {% endfor %}
      {% endif %}
    {% endwith %}

    {% block content %}{% endblock %}
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
{% block scripts %}{% endblock %}
</body>
</html>
<!-- base.html -->