{% extends "base.html" %}
{% block title %}Tableau : {{ board.title }}{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h2><i class="fas fa-clipboard-list me-2"></i>{{ board.title }}</h2>
        {% if board.description %}
            <p class="text-muted">{{ board.description }}</p>
        {% endif %}
    </div>
    <div>
        <a href="{{ url_for('main.dashboard') }}" class="btn btn-outline-secondary me-2">
            <i class="fas fa-arrow-left me-1"></i>Retour au tableau de bord
        </a>
        <a href="{{ url_for('main.add_column', board_id=board.id) }}" class="btn btn-success">
            <i class="fas fa-plus me-1"></i>Ajouter une colonne
        </a>
    </div>
</div>

<!-- Formulaire d'ajout de nouvelle tâche -->
<div class="card mb-4">
    <div class="card-header bg-info text-white">
        <h5 class="mb-0"><i class="fas fa-plus me-2"></i>Ajouter une nouvelle tâche</h5>
    </div>
    <div class="card-body">
        <form method="POST">
            {{ form.hidden_tag() }}
            <div class="row">
                <div class="col-md-3">
                    {{ form.title.label(class="form-label") }}
                    {{ form.title(class="form-control") }}
                </div>
                <div class="col-md-3">
                    {{ form.description.label(class="form-label") }}
                    {{ form.description(class="form-control") }}
                </div>
                <div class="col-md-2">
                    {{ form.status.label(class="form-label") }}
                    {{ form.status(class="form-select") }}
                </div>
                <div class="col-md-2">
                    {{ form.assigned_to.label(class="form-label") }}
                    {{ form.assigned_to(class="form-select") }}
                </div>
                {% for col in columns %}
                    <div class="col-md-2">
                        {{ form['col_' ~ col.id].label(class="form-label") }}
                        {{ form['col_' ~ col.id](class="form-control") }}
                    </div>
                {% endfor %}
                <div class="col-md-2 d-flex align-items-end">
                    {{ form.submit(class="btn btn-success w-100") }}
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Tableau des tâches -->
{% if tasks %}
    <div class="card">
        <div class="card-header">
            <h5 class="mb-0">
                <i class="fas fa-tasks me-2"></i>Tâches ({{ tasks|length }})
            </h5>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead class="table-light">
                        <tr>
                            <th>Titre</th>
                            <th>Description</th>
                            <th>Statut</th>
                            <th>Assigné à</th>
                            {% for col in columns %}
                                <th>{{ col.name }}</th>
                            {% endfor %}
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for task in tasks %}
                            <tr>
                                <td><strong>{{ task.title }}</strong></td>
                                <td>{{ task.description or '-' }}</td>
                                <td>
                                    <span class="badge {% if task.status == 'Terminé' %}bg-success{% elif task.status == 'En cours' %}bg-warning{% else %}bg-secondary{% endif %}">
                                        {{ task.status }}
                                    </span>
                                </td>
                                <td>{{ task.assigned_to.username if task.assigned_to else '-' }}</td>
                                {% for col in columns %}
                                    <td data-cell-id="{{ (task.cells|selectattr('column_id', 'equalto', col.id)|first).id if (task.cells|selectattr('column_id', 'equalto', col.id)|first) else '' }}" 
                                        class="editable-cell" 
                                        data-type="{{ col.type }}">
                                        {% set cell = (task.cells|selectattr('column_id', 'equalto', col.id)|first) %}
                                        {% if col.type == 'user' %}
                                            {% set user = users|selectattr('id', 'equalto', cell.value|int if cell and cell.value else 0)|first if cell else None %}
                                            {{ user.username if user else '-' }}
                                        {% else %}
                                            {{ cell.value if cell else '-' }}
                                        {% endif %}
                                    </td>
                                {% endfor %}
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <a href="{{ url_for('main.edit_task', task_id=task.id) }}" class="btn btn-outline-warning">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <form action="{{ url_for('main.delete_task', task_id=task.id) }}" method="POST" style="display:inline-block;" 
                                              onsubmit="return confirm('Êtes-vous sûr de vouloir supprimer cette tâche ?');">
                                            <button type="submit" class="btn btn-outline-danger">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </form>
                                    </div>
                                </td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
{% else %}
    <div class="text-center py-5">
        <div class="mb-4">
            <i class="fas fa-tasks fa-4x text-muted"></i>
        </div>
        <h4 class="text-muted">Aucune tâche dans ce tableau</h4>
        <p class="text-muted">Commencez par ajouter une nouvelle tâche en utilisant le formulaire ci-dessus</p>
    </div>
{% endif %}
{% endblock %}

{% block scripts %}
<script>
document.addEventListener("DOMContentLoaded", () => {
    document.querySelectorAll(".editable-cell").forEach(cell => {
        cell.addEventListener("click", () => {
            if (cell.querySelector("input") || cell.querySelector("select")) return;

            const spanValue = cell.textContent.trim();
            const cellId = cell.dataset.cellId;
            const type = cell.dataset.type;

            if (!cellId) return;

            let input;
            if (type === "status") {
                input = document.createElement("select");
                input.className = "form-select form-select-sm";
                ["En attente", "En cours", "Terminé"].forEach(opt => {
                    const option = document.createElement("option");
                    option.value = opt;
                    option.text = opt;
                    if (opt === spanValue) option.selected = true;
                    input.appendChild(option);
                });
            } else if(type === "date") {
                input = document.createElement("input");
                input.type = "date";
                input.className = "form-control form-control-sm";
                input.value = spanValue !== '-' ? spanValue : '';
            } else if(type === "number") {
                input = document.createElement("input");
                input.type = "number";
                input.className = "form-control form-control-sm";
                input.value = spanValue !== '-' ? spanValue : '';
            } else {
                input = document.createElement("input");
                input.type = "text";
                input.className = "form-control form-control-sm";
                input.value = spanValue !== '-' ? spanValue : '';
            }

            cell.textContent = '';
            cell.appendChild(input);
            input.focus();

            const saveValue = () => {
                const newValue = input.value;
                fetch("{{ url_for('main.update_cell') }}", {
                    method: "POST",
                    headers: {
                        "Content-Type": "application/json"
                    },
                    body: JSON.stringify({cell_id: cellId, value: newValue})
                })
                .then(res => res.json())
                .then(data => {
                    if(data.success){
                        cell.textContent = data.value || '-';
                    } else {
                        alert("خطأ في التحديث");
                        cell.textContent = spanValue;
                    }
                })
                .catch(() => {
                    alert("خطأ في التحديث");
                    cell.textContent = spanValue;
                });
            };

            input.addEventListener("blur", saveValue);
            input.addEventListener("keypress", (e) => {
                if (e.key === "Enter") {
                    saveValue();
                }
            });
        });
    });
});
</script>
{% endblock %}
