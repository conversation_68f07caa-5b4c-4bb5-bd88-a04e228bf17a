{% extends "base.html" %}
{% block title %}Tableau de bord{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="fas fa-tachometer-alt me-2"></i>Tableau de bord</h2>
    <a href="{{ url_for('main.create_board') }}" class="btn btn-primary">
        <i class="fas fa-plus me-2"></i>Créer un nouveau tableau
    </a>
</div>

{% if boards %}
    <div class="row">
        {% for board in boards %}
            <div class="col-md-6 col-lg-4 mb-4">
                <div class="card h-100">
                    <div class="card-body">
                        <h5 class="card-title">
                            <i class="fas fa-clipboard-list me-2 text-primary"></i>
                            {{ board.title }}
                        </h5>
                        <p class="card-text text-muted">
                            {{ board.description if board.description else 'Aucune description' }}
                        </p>
                        <div class="mt-auto">
                            <small class="text-muted">
                                <i class="fas fa-calendar me-1"></i>
                                Créé le : {{ board.created_at.strftime('%Y-%m-%d') if board.created_at else 'Non spécifié' }}
                            </small>
                        </div>
                    </div>
                    <div class="card-footer bg-transparent">
                        <div class="d-flex justify-content-between">
                            <a href="{{ url_for('main.board_detail', board_id=board.id) }}" class="btn btn-outline-primary btn-sm">
                                <i class="fas fa-eye me-1"></i>Voir
                            </a>
                            <div>
                                <span class="badge bg-secondary">
                                    <i class="fas fa-tasks me-1"></i>
                                    {{ board.tasks|length }} tâche{{ 's' if board.tasks|length != 1 else '' }}
                                </span>
                                <span class="badge bg-info">
                                    <i class="fas fa-columns me-1"></i>
                                    {{ board.columns|length }} colonne{{ 's' if board.columns|length != 1 else '' }}
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        {% endfor %}
    </div>
{% else %}
    <div class="text-center py-5">
        <div class="mb-4">
            <i class="fas fa-clipboard-list fa-5x text-muted"></i>
        </div>
        <h4 class="text-muted">Aucun tableau pour le moment</h4>
        <p class="text-muted">Commencez par créer un nouveau tableau pour gérer vos tâches</p>
        <a href="{{ url_for('main.create_board') }}" class="btn btn-primary btn-lg">
            <i class="fas fa-plus me-2"></i>Créer le premier tableau
        </a>
    </div>
{% endif %}
{% endblock %}
