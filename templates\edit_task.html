{% extends "base.html" %}
{% block title %}Modifier la tâche{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-10">
        <div class="card">
            <div class="card-header bg-warning text-dark">
                <h4 class="mb-0">
                    <i class="fas fa-edit me-2"></i>Modifier la tâche : {{ task.title }}
                </h4>
                <small>Dans le tableau : {{ board.title }}</small>
            </div>
            <div class="card-body">
                <form method="POST">
                    {{ form.hidden_tag() }}
                    
                    <div class="row mb-3">
                        <div class="col-md-6">
                            {{ form.title.label(class="form-label") }}
                            {{ form.title(class="form-control") }}
                            {% if form.title.errors %}
                                <div class="text-danger">
                                    {% for error in form.title.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        <div class="col-md-6">
                            {{ form.description.label(class="form-label") }}
                            {{ form.description(class="form-control") }}
                            {% if form.description.errors %}
                                <div class="text-danger">
                                    {% for error in form.description.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="row mb-3">
                        <div class="col-md-6">
                            {{ form.status.label(class="form-label") }}
                            {{ form.status(class="form-select") }}
                            {% if form.status.errors %}
                                <div class="text-danger">
                                    {% for error in form.status.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        <div class="col-md-6">
                            {{ form.assigned_to.label(class="form-label") }}
                            {{ form.assigned_to(class="form-select") }}
                            {% if form.assigned_to.errors %}
                                {% for error in form.assigned_to.errors %}
                                    <div class="text-danger">
                                        <small>{{ error }}</small>
                                    </div>
                                {% endfor %}
                            {% endif %}
                        </div>
                    </div>
                    
                    <!-- Colonnes personnalisées -->
                    {% if board.columns %}
                        <hr>
                        <h5><i class="fas fa-columns me-2"></i>Données personnalisées</h5>
                        <div class="row">
                            {% for col in board.columns %}
                                <div class="col-md-6 mb-3">
                                    {{ form['col_' ~ col.id].label(class="form-label") }}
                                    <div class="input-group">
                                        <span class="input-group-text">
                                            {% if col.type == 'text' %}
                                                <i class="fas fa-font"></i>
                                            {% elif col.type == 'number' %}
                                                <i class="fas fa-hashtag"></i>
                                            {% elif col.type == 'date' %}
                                                <i class="fas fa-calendar"></i>
                                            {% elif col.type == 'status' %}
                                                <i class="fas fa-flag"></i>
                                            {% elif col.type == 'user' %}
                                                <i class="fas fa-user"></i>
                                            {% endif %}
                                        </span>
                                        {{ form['col_' ~ col.id](class="form-control") }}
                                    </div>
                                </div>
                            {% endfor %}
                        </div>
                    {% endif %}
                    
                    <hr>
                    <div class="d-flex justify-content-between">
                        <a href="{{ url_for('main.board_detail', board_id=board.id) }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-2"></i>Annuler
                        </a>
                        {{ form.submit(class="btn btn-warning") }}
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}
