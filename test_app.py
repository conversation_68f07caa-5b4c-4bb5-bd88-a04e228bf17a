#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import time
import sys

def test_application():
    """Test simple de l'application"""

    print("🔍 Test de connexion à l'application...")

    # Attendre un peu pour s'assurer que l'application est lancée
    time.sleep(2)

    try:
        # Test de la page d'accueil
        response = requests.get('http://127.0.0.1:5000', timeout=10)

        if response.status_code == 200:
            print("✅ L'application fonctionne correctement !")
            print(f"📊 Code de réponse : {response.status_code}")
            print(f"📏 Taille du contenu : {len(response.content)} octets")

            # Vérifier la présence d'éléments importants dans la page
            content = response.text
            if "Connexion" in content:
                print("✅ Page de connexion disponible")
            if "Créer un compte" in content:
                print("✅ Page de création de compte disponible")

            return True
        else:
            print(f"❌ Erreur de réponse : {response.status_code}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ Impossible de se connecter à l'application")
        print("💡 Assurez-vous que l'application fonctionne sur http://127.0.0.1:5000")
        return False

    except requests.exceptions.Timeout:
        print("❌ Délai de connexion expiré")
        return False

    except Exception as e:
        print(f"❌ Erreur inattendue : {e}")
        return False

if __name__ == "__main__":
    success = test_application()

    if success:
        print("\n🎉 L'application est prête à être utilisée !")
        print("🌐 Ouvrez votre navigateur et allez à : http://127.0.0.1:5000")
        sys.exit(0)
    else:
        print("\n❌ Il y a un problème avec l'application")
        sys.exit(1)
