#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import time
import sys

def test_application():
    """اختبار بسيط للتطبيق"""
    
    print("🔍 اختبار الاتصال بالتطبيق...")
    
    # انتظار قليل للتأكد من تشغيل التطبيق
    time.sleep(2)
    
    try:
        # اختبار الصفحة الرئيسية
        response = requests.get('http://127.0.0.1:5000', timeout=10)
        
        if response.status_code == 200:
            print("✅ التطبيق يعمل بشكل صحيح!")
            print(f"📊 كود الاستجابة: {response.status_code}")
            print(f"📏 حجم المحتوى: {len(response.content)} بايت")
            
            # التحقق من وجود العناصر المهمة في الصفحة
            content = response.text
            if "تسجيل الدخول" in content:
                print("✅ صفحة تسجيل الدخول متاحة")
            if "إنشاء حساب" in content:
                print("✅ صفحة إنشاء الحساب متاحة")
                
            return True
        else:
            print(f"❌ خطأ في الاستجابة: {response.status_code}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ لا يمكن الاتصال بالتطبيق")
        print("💡 تأكد من أن التطبيق يعمل على http://127.0.0.1:5000")
        return False
        
    except requests.exceptions.Timeout:
        print("❌ انتهت مهلة الاتصال")
        return False
        
    except Exception as e:
        print(f"❌ خطأ غير متوقع: {e}")
        return False

if __name__ == "__main__":
    success = test_application()
    
    if success:
        print("\n🎉 التطبيق جاهز للاستخدام!")
        print("🌐 افتح المتصفح وانتقل إلى: http://127.0.0.1:5000")
        sys.exit(0)
    else:
        print("\n❌ هناك مشكلة في التطبيق")
        sys.exit(1)
